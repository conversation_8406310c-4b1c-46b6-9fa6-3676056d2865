/**
 * Authentication Guard Component
 * 
 * Protects routes by checking authentication status and redirecting to auth screen if needed.
 * Provides a loading state while checking authentication.
 */

import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useRouter, useSegments } from 'expo-router';
import { Image } from 'expo-image';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useAuth } from '@/contexts/AuthContext';

interface AuthGuardProps {
  children: React.ReactNode;
}

export function AuthGuard({ children }: AuthGuardProps) {
  const { isAuthenticated, isLoading, user } = useAuth();
  
  const router = useRouter();
  const segments = useSegments();

  React.useEffect(() => {
    if (isLoading) return; // Don't redirect while loading

    const inAuthGroup = segments[0] === 'auth';
    const inDemoGroup = segments[0] === 'simple-okta-demo';
    const inTestGroup = segments[0] === 'okta-test';
    const inPocGroup = segments[0] === 'okta-poc';
    const inPublicRoute = inAuthGroup || inDemoGroup || inTestGroup || inPocGroup;

    if (!isAuthenticated && !inPublicRoute) {
      // User is not authenticated and not on a public route, redirect to auth
      router.replace('/auth');
    } else if (isAuthenticated && inAuthGroup) {
      // User is authenticated but on auth screen, redirect to main app
      router.replace('/(tabs)');
    }
  }, [isAuthenticated, isLoading, segments, router]);

  // Show loading screen while checking authentication
  if (isLoading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <View style={styles.loadingContent}>
          <Image 
            source={require('@assets/images/logo.svg')} 
            style={styles.logo} 
          />
          <ThemedText type="title" style={styles.title}>
            Learning Coach Community
          </ThemedText>
          <ThemedText style={styles.loadingText}>
            Loading...
          </ThemedText>
        </View>
      </ThemedView>
    );
  }

  return <>{children}</>;
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingContent: {
    alignItems: 'center',
  },
  logo: {
    width: 120,
    height: 60,
    marginBottom: 30,
  },
  title: {
    textAlign: 'center',
    marginBottom: 20,
  },
  loadingText: {
    textAlign: 'center',
    opacity: 0.7,
  },
});
