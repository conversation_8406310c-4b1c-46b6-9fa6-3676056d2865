import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import 'react-native-reanimated';
import { useColorScheme } from '@/hooks/useColorScheme';

import { Provider } from 'react-redux';
import { store } from '@/store';
import { AuthProvider } from '@/contexts/AuthContext';
import { AuthGuard } from '@/components/AuthGuard';

export default function RootLayout() {
	const colorScheme = useColorScheme();
	const [loaded] = useFonts({
		SpaceMono: require('@assets/fonts/SpaceMono-Regular.ttf'),
	});

	if (!loaded) {
		// Async font loading only occurs in development.
		return null;
	}

	return (
		<ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
			<Provider store={store}>
				<AuthProvider>
					<AuthGuard>
						<Stack>
							{/* Standalone Okta PoC - first in stack to avoid auth redirects */}
							<Stack.Screen name='okta-demo' options={{ headerShown: false, title: 'Okta PoC' }} />
							<Stack.Screen name='okta-poc' options={{ headerShown: false, title: 'Okta PoC' }} />

							<Stack.Screen name='(tabs)' options={{ headerShown: false, title: 'Home' }} />
							<Stack.Screen name='auth' options={{ headerShown: false, title: 'Sign In' }} />
							<Stack.Screen name='simple-okta-demo' options={{ headerShown: false, title: 'Simple Okta Demo' }} />
							<Stack.Screen name='okta-test' options={{ headerShown: false, title: 'Okta Test' }} />
							<Stack.Screen name='+not-found' />
							<Stack.Screen name='hello' options={{ title: 'Hello Screen' }} />
						</Stack>
					</AuthGuard>
				</AuthProvider>
			</Provider>
			<StatusBar style='auto' />
		</ThemeProvider>
	);
}
