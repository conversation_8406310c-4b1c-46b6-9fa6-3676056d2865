import { useEffect } from 'react';
import * as WebBrowser from 'expo-web-browser';
import { useAuthRequest, useAutoDiscovery } from 'expo-auth-session';
import { Button, Alert } from 'react-native';


WebBrowser.maybeCompleteAuthSession();


export default function App() {
    const oktaDomain = process.env.EXPO_PUBLIC_OKTA_ISSUER || 'https://integrator-5743111.okta.com/oauth2/default';
    const clientId = process.env.EXPO_PUBLIC_OKTA_CLIENT_ID || 'your-client-id-here';
    console.log('Okta Domain: ', oktaDomain, 'Client ID: ', clientId);
  // Endpoint
  const discovery = useAutoDiscovery(oktaDomain);
  // Request
  const [request, response, promptAsync] = useAuthRequest(
    {
      clientId: clientId,
      scopes: ['openid', 'profile'],
      redirectUri: 'https://auth.expo.dev/@charlesrmajor/learning-coach-community',
    },
    discovery
  );

  useEffect(() => {
    if (response?.type === 'success') {
      const { code } = response.params;
      Alert.alert('Success!', `Received authorization code: ${code?.substring(0, 20)}...`);
      console.log('✅ Okta auth success:', { code });
    } else if (response?.type === 'error') {
      Alert.alert('Error', response.error?.message || 'Authentication failed');
      console.error('❌ Okta auth error:', response.error);
    }
  }, [response]);

  return (
    <Button
      disabled={!request}
      title="Login"
      onPress={() => {
        promptAsync();
      }}
    />
  );
}
